<template>
  <div class="grouped-table-view">
    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <span class="selected-count">已选择 {{ selectedItems.length }} 项</span>
      </div>
      
      <div class="toolbar-right">
        <el-button-group>
          <el-button
            :type="groupCollapsed ? 'primary' : ''"
            @click="toggleAllGroups(true)"
            size="small"
          >
            <el-icon><FolderOpened /></el-icon>
            全部展开
          </el-button>
          <el-button
            :type="!groupCollapsed ? 'primary' : ''"
            @click="toggleAllGroups(false)"
            size="small"
          >
            <el-icon><Folder /></el-icon>
            全部折叠
          </el-button>
        </el-button-group>
        
        <el-button @click="exportData" size="small">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 分组表格 -->
    <div class="grouped-table">
      <div v-if="!config.groupBy" class="no-group-table">
        <!-- 无分组时的普通表格 -->
        <el-table
          ref="tableRef"
          :data="items"
          row-key="itemId"
          stripe
          border
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
        >
          <!-- 选择列特殊处理 -->
          <el-table-column
            v-if="hasSelectionColumn"
            type="selection"
            width="60"
            fixed="left"
          />

          <el-table-column
            v-for="column in nonSelectionColumns"
            :key="column.id"
            :prop="column.key"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :sortable="column.sortable ? 'custom' : false"
            :fixed="column.fixed"
            :align="column.align || 'left'"
            :resizable="enableDynamicConfig && column.resizable !== false"
          >
            <template #default="{ row, $index }">
              <TableCell
                :row="row"
                :column="column"
                :index="$index"
                :button-config="buttonConfig"
                :readonly="false"
                @cell-change="handleCellChange"
                @status-change="(itemId: string, status: string, comment: string) => handleStatusChange(itemId, status, comment)"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div v-else class="grouped-content">
        <!-- 分组显示 -->
        <div
          v-for="(group, groupKey) in groupedData"
          :key="groupKey"
          class="group-section"
        >
          <!-- 分组头部 -->
          <div class="group-header" @click="toggleGroup(groupKey)">
            <div class="group-title">
              <el-icon class="group-toggle">
                <component :is="group.collapsed ? 'ArrowRight' : 'ArrowDown'" />
              </el-icon>
              <span class="group-name">{{ groupKey }}</span>
              <el-badge :value="group.count" type="primary" class="group-count" />
            </div>
            
            <div v-if="config.showGroupSummary && group.summary" class="group-summary">
              <el-tag type="success" size="small">通过: {{ group.summary.passed }}</el-tag>
              <el-tag type="danger" size="small">不通过: {{ group.summary.failed }}</el-tag>
              <el-tag type="warning" size="small">跳过: {{ group.summary.skipped }}</el-tag>
              <el-tag type="info" size="small">待处理: {{ group.summary.pending }}</el-tag>
            </div>
          </div>

          <!-- 分组内容 -->
          <el-collapse-transition>
            <div v-show="!group.collapsed" class="group-content">
              <el-table
                :ref="(el: any) => { if (el) groupTableRefs[groupKey] = el }"
                :data="group.items"
                row-key="itemId"
                stripe
                border
                @selection-change="(selection: ExtendedReviewItem[]) => handleGroupSelectionChange(groupKey, selection)"
                @sort-change="handleSortChange"
                class="group-table"
              >
                <!-- 选择列特殊处理 -->
                <el-table-column
                  v-if="hasSelectionColumn"
                  type="selection"
                  width="60"
                  fixed="left"
                />

                <el-table-column
                  v-for="column in nonSelectionColumns"
                  :key="column.id"
                  :prop="column.key"
                  :label="column.label"
                  :width="column.width"
                  :min-width="column.minWidth"
                  :sortable="column.sortable ? 'custom' : false"
                  :fixed="column.fixed"
                  :align="column.align || 'left'"
                  :resizable="enableDynamicConfig && column.resizable !== false"
                >
                  <template #default="{ row, $index }">
                    <TableCell
                      :row="row"
                      :column="column"
                      :index="$index"
                      :button-config="buttonConfig"
                      :readonly="false"
                      @cell-change="handleCellChange"
                      @status-change="(itemId: string, status: string, comment: string) => handleStatusChange(itemId, status, comment)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-collapse-transition>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="items.length === 0" class="empty-state">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import {
  FolderOpened, Folder, Download
} from '@element-plus/icons-vue'
import {
  type ExtendedReviewItem,
  type TableViewConfig,
  type GroupedItems,
  type ExportConfig,
  type ColumnConfigUpdate,
  type ConfigSyncStatus,
  groupItemsByField,
  sortColumnsByOrder
} from '@/types/table-config'
import { type StatusButtonConfig } from '@/types/defect-config'
import TableCell from './TableCell.vue'

// Props
interface Props {
  items: ExtendedReviewItem[]
  config: TableViewConfig
  selectedItems: string[]
  buttonConfig?: StatusButtonConfig
  // 新增动态配置支持
  enableDynamicConfig?: boolean
  configSyncStatus?: ConfigSyncStatus
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  selectedItems: () => [],
  enableDynamicConfig: true
})

// Emits
interface Emits {
  (e: 'selection-change', selectedItems: string[]): void
  (e: 'sort-change', sortBy: string, sortOrder: 'asc' | 'desc'): void
  (e: 'cell-change', itemId: string, field: string, value: any): void
  (e: 'status-change', itemId: string, status: any, comment?: string): void
  (e: 'export-data', config: ExportConfig): void
  // 新增动态配置事件
  (e: 'config-update', config: TableViewConfig): void
  (e: 'column-update', update: ColumnConfigUpdate): void
  (e: 'column-resize', columnId: string, width: number): void
  (e: 'column-reorder', fromIndex: number, toIndex: number): void
}

const emit = defineEmits<Emits>()

// Reactive data
const tableRef = ref()
const groupTableRefs = ref<Record<string, any>>({})
const groupedData = ref<GroupedItems>({})
const groupCollapsed = ref(false)
const isInternalUpdate = ref(false) // 防循环标志

// Computed
const visibleColumns = computed(() => {
  const columns = props.config.columns.filter(col => col.visible)
  return props.enableDynamicConfig ? sortColumnsByOrder(columns) : columns
})

// 选择列相关计算属性
const hasSelectionColumn = computed(() => {
  return visibleColumns.value.some(col => col.key === 'selection')
})

const nonSelectionColumns = computed(() => {
  return visibleColumns.value.filter(col => col.key !== 'selection')
})



// Methods
const updateGroupedData = () => {
  if (props.config.groupBy) {
    groupedData.value = groupItemsByField(props.items, props.config.groupBy)

    // 设置默认折叠状态
    Object.keys(groupedData.value).forEach(key => {
      if (groupedData.value[key].collapsed === undefined) {
        groupedData.value[key].collapsed = props.config.defaultCollapsed || false
      }
    })
  }

  // 无论是否分组，都需要同步选择状态
  if (props.selectedItems.length > 0) {
    nextTick(() => {
      // 同步无分组表格
      if (tableRef.value && !props.config.groupBy) {
        props.items.forEach(item => {
          const shouldSelect = props.selectedItems.includes(item.itemId)
          if (shouldSelect) {
            tableRef.value.toggleRowSelection(item, true)
          }
        })
      }

      // 同步分组表格
      if (props.config.groupBy && Object.keys(groupedData.value).length > 0) {
        Object.keys(groupedData.value).forEach(groupKey => {
          const tableRef = groupTableRefs.value[groupKey]
          if (tableRef && groupedData.value[groupKey]) {
            groupedData.value[groupKey].items.forEach(item => {
              const shouldSelect = props.selectedItems.includes(item.itemId)
              if (shouldSelect) {
                tableRef.toggleRowSelection(item, true)
              }
            })
          }
        })
      }
    })
  }
}



// Watch
watch(() => props.items, () => {
  updateGroupedData()
}, { immediate: true })

watch(() => props.config.groupBy, () => {
  updateGroupedData()
}, { immediate: true })



// 组件挂载时同步选择状态
onMounted(() => {
  if (props.selectedItems.length > 0) {
    nextTick(() => {
      // 同步无分组表格
      if (tableRef.value && !props.config.groupBy) {
        props.items.forEach(item => {
          const shouldSelect = props.selectedItems.includes(item.itemId)
          if (shouldSelect) {
            tableRef.value.toggleRowSelection(item, true)
          }
        })
      }

      // 同步分组表格
      if (props.config.groupBy && Object.keys(groupedData.value).length > 0) {
        Object.keys(groupedData.value).forEach(groupKey => {
          const tableRef = groupTableRefs.value[groupKey]
          if (tableRef && groupedData.value[groupKey]) {
            groupedData.value[groupKey].items.forEach(item => {
              const shouldSelect = props.selectedItems.includes(item.itemId)
              if (shouldSelect) {
                tableRef.toggleRowSelection(item, true)
              }
            })
          }
        })
      }
    })
  }
})

// 安全的选择状态同步
watch(() => props.selectedItems, () => {
  if (isInternalUpdate.value) return // 防止循环

  nextTick(() => {
    // 同步无分组表格
    if (tableRef.value && !props.config.groupBy) {
      props.items.forEach(item => {
        const shouldSelect = props.selectedItems.includes(item.itemId)
        tableRef.value.toggleRowSelection(item, shouldSelect)
      })
    }

    // 同步分组表格
    if (props.config.groupBy && Object.keys(groupedData.value).length > 0) {
      Object.keys(groupedData.value).forEach(groupKey => {
        const tableRef = groupTableRefs.value[groupKey]
        if (tableRef && groupedData.value[groupKey]) {
          groupedData.value[groupKey].items.forEach(item => {
            const shouldSelect = props.selectedItems.includes(item.itemId)
            tableRef.toggleRowSelection(item, shouldSelect)
          })
        }
      })
    }
  })
})





const handleSelectionChange = (selection: ExtendedReviewItem[]) => {
  isInternalUpdate.value = true
  const selectedIds = selection.map(item => item.itemId)
  emit('selection-change', selectedIds)
  nextTick(() => {
    isInternalUpdate.value = false
  })
}

const handleGroupSelectionChange = (groupKey: string | number, selection: ExtendedReviewItem[]) => {
  isInternalUpdate.value = true

  // 获取当前分组的选择项
  const groupSelectedIds = selection.map(item => item.itemId)

  // 获取当前分组的所有项目ID
  const groupItemIds = groupedData.value[groupKey]?.items.map(item => item.itemId) || []

  // 获取其他分组的选择项（排除当前分组的项目）
  const otherSelectedIds = props.selectedItems.filter(id => !groupItemIds.includes(id))

  // 合并选择状态
  const allSelectedIds = [...otherSelectedIds, ...groupSelectedIds]

  emit('selection-change', allSelectedIds)

  nextTick(() => {
    isInternalUpdate.value = false
  })
}

const handleSortChange = ({ prop, order }: { prop: string, order: string }) => {
  const sortOrder = order === 'ascending' ? 'asc' : 'desc'
  emit('sort-change', prop, sortOrder)
}

const handleCellChange = (itemId: string, field: string, value: any) => {
  emit('cell-change', itemId, field, value)
}

const handleStatusChange = (itemId: string, status: any, comment?: string) => {
  emit('status-change', itemId, status, comment)
}

// 移除了 handleCellSelectionChange，现在使用 Element Plus 原生选择功能

const toggleGroup = (groupKey: string | number) => {
  if (groupedData.value[groupKey]) {
    groupedData.value[groupKey].collapsed = !groupedData.value[groupKey].collapsed
  }
}

const toggleAllGroups = (expand: boolean) => {
  Object.keys(groupedData.value).forEach(key => {
    groupedData.value[key].collapsed = !expand
  })
  groupCollapsed.value = !expand
}

const exportData = () => {
  const exportConfig: ExportConfig = {
    selectedOnly: props.selectedItems.length > 0,
    includeCustomFields: true,
    format: 'excel',
    columns: visibleColumns.value.map(col => col.key)
  }

  emit('export-data', exportConfig)
}

// 动态配置相关方法已移除，如需要请重新实现
</script>

<style scoped>
.grouped-table-view {
  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px 6px 0 0;
    border: 1px solid #e4e7ed;
    border-bottom: none;
  }

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .selected-count {
    font-size: 14px;
    color: #606266;
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .grouped-table {
    border: 1px solid #e4e7ed;
    border-radius: 0 0 6px 6px;
    overflow: hidden;
  }

  .no-group-table {
    .el-table {
      border-radius: 0;
    }
  }

  .grouped-content {
    .group-section {
      border-bottom: 1px solid #e4e7ed;
    }

    .group-section:last-child {
      border-bottom: none;
    }

    .group-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #fafbfc;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    .group-header:hover {
      background: #f0f2f5;
    }

    .group-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .group-toggle {
      transition: transform 0.3s;
      color: #909399;
    }

    .group-name {
      font-weight: 500;
      color: #303133;
    }

    .group-count {
      margin-left: 8px;
    }

    .group-summary {
      display: flex;
      gap: 8px;
    }

    .group-content {
      .group-table {
        border: none;
        border-radius: 0;
      }

      .group-table :deep(.el-table__header) {
        display: none;
      }
    }
  }

  .empty-state {
    padding: 40px;
    text-align: center;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    background: #fff;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grouped-table-view {
    .table-toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .toolbar-left,
    .toolbar-right {
      justify-content: center;
    }

    .group-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .group-summary {
      flex-wrap: wrap;
    }
  }
}
</style>
