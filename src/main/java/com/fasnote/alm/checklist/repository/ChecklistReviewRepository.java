package com.fasnote.alm.checklist.repository;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import com.fasnote.alm.checklist.model.ChecklistReview;

/**
 * 检查单评审仓库接口
 */
public interface ChecklistReviewRepository {

    /**
     * 保存评审实例
     *
     * @param projectId 项目ID
     * @param entity 评审实例
     * @return 保存后的评审实例
     * @throws IOException IO异常
     */
    ChecklistReview save(String projectId, ChecklistReview entity) throws IOException;

    /**
     * 根据项目ID和评审ID查找评审实例
     *
     * @param projectId 项目ID
     * @param id 评审ID（等同于工作项ID）
     * @return 评审实例的Optional包装
     * @throws IOException IO异常
     */
    Optional<ChecklistReview> findById(String projectId, String id) throws IOException;

    /**
     * 查找项目下所有评审实例
     *
     * @param projectId 项目ID
     * @return 所有评审实例列表
     * @throws IOException IO异常
     */
    List<ChecklistReview> findAll(String projectId) throws IOException;

    /**
     * 检查评审实例是否存在
     *
     * @param projectId 项目ID
     * @param id 评审ID
     * @return 是否存在
     * @throws IOException IO异常
     */
    boolean existsById(String projectId, String id) throws IOException;

    /**
     * 根据ID删除评审实例
     *
     * @param projectId 项目ID
     * @param id 评审ID
     * @throws IOException IO异常
     */
    void deleteById(String projectId, String id) throws IOException;

    /**
     * 删除评审实例
     *
     * @param projectId 项目ID
     * @param entity 评审实例
     * @throws IOException IO异常
     */
    void delete(String projectId, ChecklistReview entity) throws IOException;

    /**
     * 删除项目下所有评审实例
     *
     * @param projectId 项目ID
     * @throws IOException IO异常
     */
    void deleteAll(String projectId) throws IOException;

    /**
     * 统计项目下评审实例数量
     *
     * @param projectId 项目ID
     * @return 评审实例数量
     * @throws IOException IO异常
     */
    long count(String projectId) throws IOException;

    /**
     * 根据模板ID查找评审
     *
     * @param projectId 项目ID
     * @param templateId 模板ID
     * @return 符合条件的评审列表
     * @throws IOException IO异常
     */
    List<ChecklistReview> findByTemplateId(String projectId, String templateId) throws IOException;

    /**
     * 根据类型查找评审
     *
     * @param projectId 项目ID
     * @param type 评审类型
     * @return 符合条件的评审列表
     * @throws IOException IO异常
     */
    List<ChecklistReview> findByType(String projectId, String type) throws IOException;

    /**
     * 根据状态查找评审
     *
     * @param projectId 项目ID
     * @param status 评审状态
     * @return 符合条件的评审列表
     * @throws IOException IO异常
     */
    List<ChecklistReview> findByStatus(String projectId, String status) throws IOException;

    /**
     * 根据创建时间范围查找评审
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 符合条件的评审列表
     * @throws IOException IO异常
     */
    List<ChecklistReview> findByCreatedTimeBetween(LocalDateTime startTime, LocalDateTime endTime) throws IOException;

    /**
     * 根据模板ID和版本查找评审
     *
     * @param templateId 模板ID
     * @param templateVersion 模板版本
     * @return 符合条件的评审列表
     * @throws IOException IO异常
     */
    List<ChecklistReview> findByTemplateIdAndVersion(String templateId, String templateVersion) throws IOException;

    /**
     * 获取最近创建的评审列表
     *
     * @param limit 限制数量
     * @return 最近创建的评审列表
     * @throws IOException IO异常
     */
    List<ChecklistReview> findRecentReviews(int limit) throws IOException;

    /**
     * 统计指定状态的评审数量
     *
     * @param status 评审状态
     * @return 评审数量
     * @throws IOException IO异常
     */
    long countByStatus(String status) throws IOException;

    /**
     * 统计指定类型的评审数量
     *
     * @param type 评审类型
     * @return 评审数量
     * @throws IOException IO异常
     */
    long countByType(String type) throws IOException;

    /**
     * 检查指定模板是否有关联的评审
     *
     * @param templateId 模板ID
     * @return 是否有关联评审
     * @throws IOException IO异常
     */
    boolean existsByTemplateId(String templateId) throws IOException;
}