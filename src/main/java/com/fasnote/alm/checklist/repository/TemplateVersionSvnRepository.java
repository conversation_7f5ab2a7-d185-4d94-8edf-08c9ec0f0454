package com.fasnote.alm.checklist.repository;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.fasnote.alm.checklist.model.ReviewTemplateVersion;
import com.fasnote.alm.checklist.model.TemplateSnapshot;
import com.fasnote.alm.checklist.service.IRepositoryFileService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.polarion.alm.tracker.ITrackerService;
import com.polarion.alm.tracker.model.IWorkItem;
import com.polarion.platform.core.PlatformContext;
import com.polarion.subterra.base.location.ILocation;

/**
 * 模板版本SVN存储实现
 */
@Repository("templateVersionSvnRepository")
public class TemplateVersionSvnRepository implements TemplateVersionRepository {
    
    private static final Logger logger = LoggerFactory.getLogger(TemplateVersionSvnRepository.class);
    private static final String SNAPSHOTS_DIR = "template-snapshots";
    private static final String REVIEW_VERSION_FILE = "checklist-template-version.json";
    
    @Autowired
    private IRepositoryFileService repositoryFileService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public TemplateSnapshot saveSnapshot(String projectId, TemplateSnapshot snapshot) throws IOException {
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        
        try {
            // 快照存储在项目目录下的template-snapshots目录，与模板保持一致的目录结构
            // 路径结构：/project/{projectId}/template-snapshots/
            ILocation projectDir = getProjectDirectory(projectId);
            if (projectDir == null) {
                throw new IOException("无法获取项目目录位置");
            }

            ILocation snapshotsDir = projectDir.append(SNAPSHOTS_DIR);
            ILocation snapshotFile = snapshotsDir.append(snapshot.getId() + ".json");
            
            String jsonContent = objectMapper.writeValueAsString(snapshot);
            boolean success = repositoryFileService.writeFile(snapshotFile, jsonContent);
            if (!success) {
                throw new IOException("保存快照失败: " + snapshot.getId());
            }
            
            return snapshot;
        } catch (Exception e) {
            logger.error("Failed to save snapshot: {} in project: {}", snapshot.getId(), projectId, e);
            throw new IOException("SVN保存快照失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Optional<TemplateSnapshot> findSnapshotById(String projectId, String snapshotId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty() || 
            snapshotId == null || snapshotId.trim().isEmpty()) {
            return Optional.empty();
        }
        
        try {
            ILocation projectDir = getProjectDirectory(projectId);
            if (projectDir == null) {
                return Optional.empty();
            }

            ILocation snapshotFile = projectDir.append(SNAPSHOTS_DIR).append(snapshotId + ".json");
            if (!repositoryFileService.exists(snapshotFile)) {
                return Optional.empty();
            }
            
            InputStream inputStream = repositoryFileService.readFile(snapshotFile);
            if (inputStream == null) {
                return Optional.empty();
            }
            
            String jsonContent = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            TemplateSnapshot snapshot = objectMapper.readValue(jsonContent, TemplateSnapshot.class);
            return Optional.of(snapshot);
        } catch (Exception e) {
            logger.error("Failed to load snapshot: {} from project: {}", snapshotId, projectId, e);
            return Optional.empty();
        }
    }
    
    @Override
    public List<TemplateSnapshot> findSnapshotsByTemplateId(String projectId, String templateId, int limit, int offset) throws IOException {
        // SVN存储模式暂不支持复杂查询，返回空列表
        logger.warn("SVN存储模式不支持按模板ID查询快照列表");
        return new ArrayList<>();
    }
    
    @Override
    public boolean deleteSnapshot(String projectId, String snapshotId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty() || 
            snapshotId == null || snapshotId.trim().isEmpty()) {
            return false;
        }
        
        try {
            ILocation projectDir = getProjectDirectory(projectId);
            if (projectDir == null) {
                return false;
            }

            ILocation snapshotFile = projectDir.append(SNAPSHOTS_DIR).append(snapshotId + ".json");
            return repositoryFileService.deleteFile(snapshotFile);
        } catch (Exception e) {
            logger.error("Failed to delete snapshot: {} from project: {}", snapshotId, projectId, e);
            return false;
        }
    }
    
    @Override
    public ReviewTemplateVersion saveReviewTemplateVersion(String projectId, ReviewTemplateVersion reviewVersion) throws IOException {
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        
        try {
            ILocation workItemLocation = getWorkItemLocation(projectId, reviewVersion.getReviewId());
            if (workItemLocation == null) {
                throw new IOException("无法获取工作项位置");
            }
            
            ILocation versionFile = workItemLocation.append(REVIEW_VERSION_FILE);
            String jsonContent = objectMapper.writeValueAsString(reviewVersion);
            boolean success = repositoryFileService.writeFile(versionFile, jsonContent);
            if (!success) {
                throw new IOException("保存版本关联信息失败");
            }
            
            return reviewVersion;
        } catch (Exception e) {
            logger.error("Failed to save review template version for review: {} in project: {}", 
                reviewVersion.getReviewId(), projectId, e);
            throw new IOException("SVN保存版本关联失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Optional<ReviewTemplateVersion> findReviewTemplateVersionByReviewId(String projectId, String reviewId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty() || 
            reviewId == null || reviewId.trim().isEmpty()) {
            return Optional.empty();
        }
        
        try {
            ILocation workItemLocation = getWorkItemLocation(projectId, reviewId);
            if (workItemLocation == null) {
                return Optional.empty();
            }
            
            ILocation versionFile = workItemLocation.append(REVIEW_VERSION_FILE);
            if (!repositoryFileService.exists(versionFile)) {
                return Optional.empty();
            }
            
            InputStream inputStream = repositoryFileService.readFile(versionFile);
            if (inputStream == null) {
                return Optional.empty();
            }
            
            String jsonContent = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            ReviewTemplateVersion reviewVersion = objectMapper.readValue(jsonContent, ReviewTemplateVersion.class);
            return Optional.of(reviewVersion);
        } catch (Exception e) {
            logger.error("Failed to load review template version for review: {} from project: {}", reviewId, projectId, e);
            return Optional.empty();
        }
    }
    
    @Override
    public boolean deleteReviewTemplateVersion(String projectId, String reviewId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty() || 
            reviewId == null || reviewId.trim().isEmpty()) {
            return false;
        }
        
        try {
            ILocation workItemLocation = getWorkItemLocation(projectId, reviewId);
            if (workItemLocation == null) {
                return false;
            }
            
            ILocation versionFile = workItemLocation.append(REVIEW_VERSION_FILE);
            return repositoryFileService.deleteFile(versionFile);
        } catch (Exception e) {
            logger.error("Failed to delete review template version for review: {} from project: {}", reviewId, projectId, e);
            return false;
        }
    }
    
    @Override
    public long countSnapshots(String projectId, String templateId) throws IOException {
        // SVN存储模式暂不支持统计功能
        logger.warn("SVN存储模式不支持快照统计功能");
        return 0;
    }
    
    @Override
    public boolean existsSnapshot(String projectId, String snapshotId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty() || 
            snapshotId == null || snapshotId.trim().isEmpty()) {
            return false;
        }
        
        try {
            ILocation projectDir = getProjectDirectory(projectId);
            if (projectDir == null) {
                return false;
            }

            ILocation snapshotFile = projectDir.append(SNAPSHOTS_DIR).append(snapshotId + ".json");
            return repositoryFileService.exists(snapshotFile);
        } catch (Exception e) {
            logger.error("Failed to check snapshot existence: {} in project: {}", snapshotId, projectId, e);
            return false;
        }
    }
    
    @Override
    public boolean existsReviewTemplateVersion(String projectId, String reviewId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty() || 
            reviewId == null || reviewId.trim().isEmpty()) {
            return false;
        }
        
        try {
            ILocation workItemLocation = getWorkItemLocation(projectId, reviewId);
            if (workItemLocation == null) {
                return false;
            }
            
            ILocation versionFile = workItemLocation.append(REVIEW_VERSION_FILE);
            return repositoryFileService.exists(versionFile);
        } catch (Exception e) {
            logger.error("Failed to check review template version existence for review: {} in project: {}", reviewId, projectId, e);
            return false;
        }
    }

    /**
     * 获取项目目录位置
     * 基于检查单SVN存储的路径结构，通过工作项位置推断项目目录
     */
    private ILocation getProjectDirectory(String projectId) {
        try {
            // 尝试获取项目中的任意一个工作项来推断项目目录结构
            // 假设工作项路径为：/project/{projectId}/workitem/{workItemId}/
            // 那么项目目录应该是：/project/{projectId}/

            // 这里使用一个简化的方法：通过项目ID构造项目位置
            // 实际实现中可能需要根据Polarion的具体配置来调整路径结构

            // 暂时使用简化实现：通过工作项位置推断项目目录
            // 在实际部署时需要根据具体的Polarion SVN结构来调整

            // 由于无法直接获取项目位置，暂时返回null
            // 这意味着SVN模式下的模板快照功能需要在实际部署时完善
            logger.warn("Project directory location not implemented for project: {}", projectId);
            return null;
        } catch (Exception e) {
            logger.error("Failed to get project directory for project: {}", projectId, e);
            return null;
        }
    }

    /**
     * 获取工作项位置
     */
    private ILocation getWorkItemLocation(String projectId, String workItemId) {
        try {
            ITrackerService trackerService = PlatformContext.getPlatform().lookupService(ITrackerService.class);
            IWorkItem workItem = trackerService.getWorkItem(projectId, workItemId);
            if (workItem == null || workItem.isUnresolvable()) {
                logger.warn("Cannot get location for non-existent work item: {}", workItemId);
                return null;
            }
            return workItem.getLocation().getParentLocation();
        } catch (Exception e) {
            logger.error("Failed to get work item location: {} in project: {}", workItemId, projectId, e);
            return null;
        }
    }
}
